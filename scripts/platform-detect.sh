#!/bin/bash
# Platform detection script for basher

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# Detect architecture
detect_arch() {
    case "$(uname -m)" in
        x86_64) echo "x86_64" ;;
        arm64|aarch64) echo "arm64" ;;
        *) echo "unknown" ;;
    esac
}

# Detect package managers
detect_package_manager() {
    local os=$(detect_os)

    if [[ "$os" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            echo "homebrew"
        else
            echo "none"
        fi
    elif [[ "$os" == "linux" ]]; then
        if command -v apt &> /dev/null; then
            echo "apt"
        elif command -v yum &> /dev/null; then
            echo "yum"
        elif command -v dnf &> /dev/null; then
            echo "dnf"
        else
            echo "none"
        fi
    else
        echo "none"
    fi
}

# Get Homebrew prefix
get_homebrew_prefix() {
    if command -v brew &> /dev/null; then
        brew --prefix
    else
        echo ""
    fi
}

# Detect Homebrew installation type
detect_homebrew_type() {
    local prefix=$(get_homebrew_prefix)
    if [[ "$prefix" == "/opt/homebrew" ]]; then
        echo "apple_silicon"
    elif [[ "$prefix" == "/usr/local" ]]; then
        echo "intel"
    else
        echo "unknown"
    fi
}

# Main detection function
detect_platform() {
    local os=$(detect_os)
    local arch=$(detect_arch)
    local pkg_manager=$(detect_package_manager)

    echo "OS: $os"
    echo "Architecture: $arch"
    echo "Package Manager: $pkg_manager"

    if [[ "$os" == "macos" ]]; then
        local homebrew_type=$(detect_homebrew_type)
        echo "Homebrew Type: $homebrew_type"
        echo "Homebrew Prefix: $(get_homebrew_prefix)"
    fi
}

# Export functions for use in other scripts
export -f detect_os
export -f detect_arch
export -f detect_package_manager
export -f get_homebrew_prefix
export -f detect_homebrew_type
export -f detect_platform
