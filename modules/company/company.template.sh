#!/bin/bash
# Template for company-specific configurations
# Copy this file to ~/.company_config and add your company-specific details

# Company-specific shortcuts - Customize for your organization
# alias app-dev-console='heroku run bundle exec rails console -a your-app-dev'
# alias app-dev-logs='heroku logs -t -a your-app-dev'
# alias app-prd-logs='heroku logs -t -a your-app-prd'

# Version control configuration - Customize for your organization
# export P4PORT=ssl:your-p4-server:1999
# export P4CLIENT=your-client-name
# export P4USER=your-username
# export P4PASSWD=
# export P4MERGE=/Applications/p4merge.app

# Add any other company-specific configurations
# export COMPANY_API_KEY="your_company_api_key"
# alias company-tool="your-company-command"
