#!/bin/bash
# Company-specific configurations

# Source company-specific configurations from external file (not tracked in git)
if [[ -f ~/.company_config ]]; then
    source ~/.company_config
fi

# Company-specific shortcuts - Define these in ~/.company_config
# Example:
# alias app-dev-console='heroku run bundle exec rails console -a your-app-dev'
# alias app-dev-logs='heroku logs -t -a your-app-dev'
# alias app-prd-logs='heroku logs -t -a your-app-prd'

# Version control configuration - Define these in ~/.company_config
# Example:
# export P4PORT=ssl:your-p4-server:1999
# export P4CLIENT=your-client-name
# export P4USER=your-username
# export P4PASSWD=
# export P4MERGE=/Applications/p4merge.app
