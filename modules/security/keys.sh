#!/bin/bash
# Security and API keys configuration

# Source important keys from external file (not tracked in git)
if [[ -f ~/.bash_keys ]]; then
    source ~/.bash_keys
fi

# API keys and tokens - Set these as environment variables or in ~/.bash_keys
# Example: export CODEBUILDER_STAGING_API_KEY="your_key_here"
# export HONEYCOMB_API_KEY="your_key_here"
# export HOMEBREW_GITHUB_API_TOKEN="your_token_here"
# export GITHUB_PACKAGES_TOKEN="your_token_here"
# export BUNDLE_GEM__FURY__IO="your_token_here"
# export SUMOLOGIC_ACCESSID="your_access_id_here"
# export SUMOLOGIC_ACCESSKEY="your_access_key_here"
# export GIT_DOWNLOAD_TOKEN="your_token_here"
# export GITHUB_TOKEN="your_token_here"
