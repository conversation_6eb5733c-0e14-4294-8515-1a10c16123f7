#!/bin/bash
# SSH configuration and key management

# Set environment variable to suppress deprecated flag warnings
export APPLE_SSH_ADD_BEHAVIOR=macos

# Start SSH agent and add keys
eval "$(ssh-agent -s)"

# Add SSH keys if they exist, using modern flags
if [[ -f ~/.ssh/id_rsa ]]; then
    ssh-add --apple-use-keychain ~/.ssh/id_rsa 2>/dev/null || ssh-add ~/.ssh/id_rsa 2>/dev/null
fi

if [[ -f ~/.ssh/github_rsa ]]; then
    ssh-add --apple-use-keychain ~/.ssh/github_rsa 2>/dev/null || ssh-add ~/.ssh/github_rsa 2>/dev/null
fi
