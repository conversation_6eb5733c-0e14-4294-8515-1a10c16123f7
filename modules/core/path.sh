#!/bin/bash
# OS-agnostic PATH management and system paths

# Source platform detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/scripts/platform-detect.sh"

# Update PATH to include personal bin if it exists
[ -d "$HOME/bin" ] && PATH="$HOME/bin:$PATH"

# Get OS and package manager info
local os=$(detect_os)
local pkg_manager=$(detect_package_manager)

# Platform-specific PATH additions
if [[ "$os" == "macos" ]]; then
    # macOS-specific paths
    local homebrew_prefix=$(get_homebrew_prefix)

    # Python paths
    export PATH="$homebrew_prefix/opt/python@3.8/bin:/usr/local/bin/pinentry:$PATH"

    # Homebrew paths
    export PATH="$homebrew_prefix/opt/openjdk/bin:$PATH"
    export PATH="$homebrew_prefix/opt/libxslt/bin:$PATH"
    export PATH="$homebrew_prefix/opt/icu4c/bin:$PATH"
    export PATH="$homebrew_prefix/opt/icu4c/sbin:$PATH"

    # pipx path
    export PATH="$PATH:$HOME/.local/bin"

elif [[ "$os" == "linux" ]]; then
    # Linux-specific paths
    case "$pkg_manager" in
        "apt")
            # Ubuntu/Debian paths
            export PATH="/usr/local/bin:/usr/bin:$PATH"
            ;;
        "yum"|"dnf")
            # CentOS/RHEL/Fedora paths
            export PATH="/usr/local/bin:/usr/bin:$PATH"
            ;;
    esac

    # pipx path for Linux
    export PATH="$PATH:$HOME/.local/bin"
fi

# Node modules path (cross-platform)
export PATH="$HOME/node_modules/.bin/:$PATH"

# Personal bin path (cross-platform)
export PATH="$HOME/bin:$PATH"
