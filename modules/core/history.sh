#!/bin/bash
# Bash history configuration

# Increase Bash history size. Allow 32³ entries; the default is 500.
export HISTSIZE='32768';
export HISTFILESIZE="${HISTSIZE}";

# Omit duplicates and commands that begin with a space from history.
export HISTCONTROL='ignoreboth';

# Don't log these to the history
export HISTIGNORE=" *:ls:cd:cd -:df:pwd:exit:date:* --help:* -h:bg:fg:history:clear;"

# When the shell exits, append to the history file instead of overwriting it
shopt -s histappend
