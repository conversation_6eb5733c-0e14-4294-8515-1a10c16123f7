#!/bin/bash
# Core environment variables and settings

# Basic environment variables
export PGUSER=postgres
export PGHOST=localhost
export EDITOR=/usr/bin/vim

# LS colors for better directory listing
export LSCOLORS="Gxfxcxdxbxegedabagacad"

# GPG configuration
export GPG_TTY=$(tty)
# Launch GPG agent if available, suppress errors if not configured
if command -v gpgconf &> /dev/null; then
    gpgconf --launch gpg-agent 2>/dev/null || true
fi

# Library path for Homebrew
export LIBRARY_PATH=/opt/homebrew/lib
