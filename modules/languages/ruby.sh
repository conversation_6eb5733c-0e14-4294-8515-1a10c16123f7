#!/bin/bash
# Ruby environment configuration

# rbenv configuration (only if rbenv is installed)
if command -v rbenv &> /dev/null; then
    eval "$(rbenv init -)"
    # Set global Ruby version if not already set
    if ! rbenv global &> /dev/null; then
        rbenv global 3.2.2 2>/dev/null || true
    fi
fi

# Ruby gem path - Customize for your Ruby version (only if ruby is available)
if command -v ruby &> /dev/null; then
    export PATH="$HOME/.gem/ruby/$(ruby -e 'puts RUBY_VERSION')/bin:$PATH"
fi
