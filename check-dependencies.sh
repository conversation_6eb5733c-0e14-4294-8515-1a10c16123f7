#!/bin/bash
# OS-agnostic dependency checker script

# Source platform detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/scripts/platform-detect.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# Check if a command exists
check_command() {
    local cmd="$1"
    local name="$2"
    local install_cmd="$3"

    if command -v "$cmd" >/dev/null 2>&1; then
        echo -e "✅ $name"
        return 0
    else
        echo -e "❌ $name"
        if [[ -n "$install_cmd" ]]; then
            echo -e "   Install with: $install_cmd"
        fi
        return 1
    fi
}

# Get installation command for current platform
get_install_cmd() {
    local package="$1"
    local os=$(detect_os)
    local pkg_manager=$(detect_package_manager)

    # Special cases for specific packages
    case "$package" in
        "go")
            echo "asdf install golang latest (requires ASDF)"
            ;;
        "docker")
            if [[ "$os" == "macos" ]]; then
                echo "Download Docker Desktop from https://www.docker.com/products/docker-desktop/"
            else
                echo "Install via package manager or Docker's official repository"
            fi
            ;;
        "asdf")
            if [[ "$os" == "macos" ]]; then
                echo "brew install asdf"
            else
                echo "git clone https://github.com/asdf-vm/asdf.git ~/.asdf"
            fi
            ;;
        *)
            case "$os" in
                "macos")
                    echo "brew install $package"
                    ;;
                "linux")
                    case "$pkg_manager" in
                        "apt")
                            echo "sudo apt install $package"
                            ;;
                        "yum")
                            echo "sudo yum install $package"
                            ;;
                        "dnf")
                            echo "sudo dnf install $package"
                            ;;
                        *)
                            echo "Manual installation required"
                            ;;
                    esac
                    ;;
                *)
                    echo "Manual installation required"
                    ;;
            esac
            ;;
    esac
}

# Main dependency check function
main() {
    print_header "Platform Information"
    detect_platform
    echo ""

    local os=$(detect_os)
    local pkg_manager=$(detect_package_manager)
    local missing_count=0

    print_header "Core Tools"
    # Core tools
    check_command "git" "Git" "$(get_install_cmd git)" || ((missing_count++))
    check_command "curl" "curl" "$(get_install_cmd curl)" || ((missing_count++))
    check_command "jq" "jq" "$(get_install_cmd jq)" || ((missing_count++))

    echo ""
    print_header "Language Runtimes"
    # Language runtimes
    check_command "go" "Go" "$(get_install_cmd go)" || ((missing_count++))
    check_command "node" "Node.js" "$(get_install_cmd node)" || ((missing_count++))
    check_command "ruby" "Ruby" "$(get_install_cmd ruby)" || ((missing_count++))
    check_command "python3" "Python" "$(get_install_cmd python3)" || ((missing_count++))
    check_command "java" "Java" "$(get_install_cmd openjdk-11-jdk)" || ((missing_count++))

    echo ""
    print_header "Cloud Tools"
    # Cloud tools
    check_command "aws" "AWS CLI" "$(get_install_cmd awscli)" || ((missing_count++))
    check_command "kubectl" "kubectl" "$(get_install_cmd kubectl)" || ((missing_count++))
    check_command "terraform" "Terraform" "$(get_install_cmd terraform)" || ((missing_count++))
    check_command "heroku" "Heroku CLI" "$(get_install_cmd heroku)" || ((missing_count++))
    check_command "docker" "Docker" "$(get_install_cmd docker)" || ((missing_count++))

    echo ""
    print_header "Development Tools"
    # Development tools
    check_command "ansible" "Ansible" "$(get_install_cmd ansible)" || ((missing_count++))
    check_command "starship" "Starship" "$(get_install_cmd starship)" || ((missing_count++))

    echo ""
    print_header "Package Managers"
    # Package managers
    if [[ "$os" == "macos" ]]; then
        check_command "brew" "Homebrew" "/bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"" || ((missing_count++))
    else
        case "$pkg_manager" in
            "apt")
                check_command "apt" "apt" "Pre-installed on Ubuntu/Debian" || ((missing_count++))
                ;;
            "yum")
                check_command "yum" "yum" "Pre-installed on CentOS/RHEL" || ((missing_count++))
                ;;
            "dnf")
                check_command "dnf" "dnf" "Pre-installed on Fedora" || ((missing_count++))
                ;;
            *)
                print_warning "No supported package manager detected"
                ((missing_count++))
                ;;
        esac
    fi

    echo ""
    print_header "Version Managers"
    # Version managers
    check_command "asdf" "ASDF" "$(get_install_cmd asdf)" || ((missing_count++))

    echo ""
    print_header "Summary"
    if [[ $missing_count -eq 0 ]]; then
        print_status "All dependencies are installed! 🎉"
    else
        print_warning "$missing_count dependencies are missing"
        echo ""
        print_status "To install missing dependencies, run:"
        echo "  ./install-dependencies.sh"
        echo ""
        print_status "Or install them manually using the commands shown above"
    fi

    echo ""
    print_header "Platform-Specific Notes"
    case "$os" in
        "macos")
            echo "• Homebrew is the primary package manager"
            echo "• ASDF is used for language version management (Go, etc.)"
            echo "• Docker Desktop must be installed manually from the official website"
            echo "• Some tools may need manual installation"
            echo "• Consider installing iTerm2 for better terminal experience"
            ;;
        "linux")
            echo "• Using system package manager: $pkg_manager"
            echo "• ASDF is used for language version management (Go, etc.)"
            echo "• Some tools may need additional repositories"
            echo "• Docker CE can be installed via package manager"
            ;;
        *)
            print_error "Unsupported operating system"
            ;;
    esac
}

# Run main function
main "$@"
