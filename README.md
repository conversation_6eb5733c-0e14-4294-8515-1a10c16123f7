# Basher - Modular Bash Profile

A modular bash profile system that organizes your shell environment into categorized modules for easy maintenance and portability across multiple machines.

## Features

- **Modular Design**: Organized into logical categories (core, languages, cloud, tools, etc.)
- **Portable**: Easy to share and install on different machines
- **Configurable**: Enable/disable specific modules as needed
- **Maintainable**: Clear separation of concerns
- **Secure**: Sensitive data managed separately

## Installation

### Prerequisites

Before installing basher, ensure you have the required dependencies. See [DEPENDENCIES.md](DEPENDENCIES.md) for a complete list.

**Quick dependency check:**
```bash
./check-dependencies.sh
```

**Install all dependencies (macOS):**
```bash
./install-dependencies.sh
```

### Quick Start

1. Clone this repository:
   ```bash
   git clone <your-repo-url> ~/.basher
   cd ~/.basher
   ```

2. Set up your API keys (optional):
   ```bash
   # Copy the template and add your keys
   cp modules/security/keys.template.sh ~/.bash_keys
   # Edit ~/.bash_keys with your actual API keys
   ```

3. Set up your company configuration (optional):
   ```bash
   # Copy the template and add your company-specific details
   cp modules/company/company.template.sh ~/.company_config
   # Edit ~/.company_config with your company-specific configurations
   ```

4. Add to your `~/.bash_profile`:
   ```bash
   # Source basher
   source ~/.basher/install.sh
   ```

5. Restart your terminal or run:
   ```bash
   source ~/.bash_profile
   ```

## Module Structure

```
modules/
├── core/           # Essential system configurations
├── languages/      # Development language environments
├── cloud/          # Cloud and infrastructure tools
├── tools/          # Development and DevOps tools
├── security/       # Security and authentication
├── productivity/   # Workflow and productivity tools
└── company/        # Company-specific configurations
```

## Module Descriptions

### Core Modules (`modules/core/`)

- **environment.sh**: Basic environment variables, editor settings, GPG configuration
- **history.sh**: Bash history configuration and settings
- **path.sh**: PATH management and system paths
- **ssh.sh**: SSH agent setup and key management

### Language Modules (`modules/languages/`)

- **go.sh**: Go environment, GOPATH, Go binary path
- **nodejs.sh**: NVM setup, Yarn configuration, Node.js paths
- **ruby.sh**: rbenv setup, bundler aliases, Ruby paths
- **python.sh**: pipx, Python paths, Python completion
- **java.sh**: JAVA_HOME, Java binary paths

### Cloud Modules (`modules/cloud/`)

- **aws.sh**: AWS CLI, eksctl, AWS-related functions
- **kubernetes.sh**: kubectl aliases, kcontext function
- **terraform.sh**: Terraform formatting, login, and utilities

### Tools Modules (`modules/tools/`)

- **git.sh**: Git aliases, branch management, status functions
- **docker.sh**: Docker aliases and utilities
- **ansible.sh**: Ansible environment setup
- **starship.sh**: Starship prompt configuration

### Security Modules (`modules/security/`)

- **keys.sh**: Security configuration (no actual keys stored)
- **keys.template.sh**: Template for adding your own API keys

### Productivity Modules (`modules/productivity/`)

- **aliases.sh**: General aliases (vi, ls, etc.)
- **functions.sh**: Custom bash functions
- **workspace.sh**: Workspace shortcuts and navigation

### Company Modules (`modules/company/`)

- **company.sh**: Generic company configuration (sources external file)
- **company.template.sh**: Template for company-specific configurations

## Configuration

### Customizing Modules

Each module can be customized by editing the corresponding `.sh` file in the `modules/` directory.

### Adding New Modules

1. Create a new `.sh` file in the appropriate module directory
2. Add your configurations to the file
3. The installer will automatically source all `.sh` files in each module directory

### Machine-Specific Settings

For machine-specific configurations, you can:

1. Create a `local/` directory in modules
2. Add machine-specific files there
3. The installer will source these after all other modules

## Security Notes

- **API keys and tokens are NOT stored in this repository**
- Sensitive data should be stored in `~/.bash_keys` (not tracked by git)
- Use the template file `modules/security/keys.template.sh` as a starting point
- Never commit API keys, tokens, or personal information to this repository
- The `.gitignore` file prevents accidental commits of sensitive files

## Troubleshooting

### Module Not Loading

- Check that the module file exists and has execute permissions
- Verify the module file has proper bash syntax
- Check the console output for any error messages

### Path Issues

- Ensure all PATH modifications are done in the appropriate module
- Check that the paths exist on your system
- Verify Homebrew and other package managers are installed

### Dependency Issues

- Run `./check-dependencies.sh` to identify missing tools
- Install missing dependencies using `./install-dependencies.sh`
- Some modules will work without all dependencies installed
- Check [DEPENDENCIES.md](DEPENDENCIES.md) for detailed requirements

### Git Integration

- The git prompt functions are included in `modules/tools/git.sh`
- Ensure git is installed and configured

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on multiple machines
5. Submit a pull request

## License

Generated by AI, so go nuts with your use.
