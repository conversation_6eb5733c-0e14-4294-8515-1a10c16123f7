#!/bin/bash
# OS-agnostic dependency installation script for basher

# Source platform detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/scripts/platform-detect.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Install Homebrew on macOS
install_homebrew() {
    if ! command -v brew &> /dev/null; then
        print_status "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

        # Add Homebrew to PATH for Apple Silicon
        local homebrew_type=$(detect_homebrew_type)
        if [[ "$homebrew_type" == "apple_silicon" ]]; then
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.bash_profile
            eval "$(/opt/homebrew/bin/brew shellenv)"
        fi
    else
        print_status "Homebrew already installed"
    fi
}

# Install packages using Homebrew (macOS)
install_with_homebrew() {
    local packages=("$@")
    for package in "${packages[@]}"; do
        if ! brew list "$package" &> /dev/null; then
            print_status "Installing $package..."
            brew install "$package"
        else
            print_status "$package already installed"
        fi
    done
}

# Install packages using apt (Ubuntu/Debian)
install_with_apt() {
    local packages=("$@")
    print_status "Updating package list..."
    sudo apt update
    for package in "${packages[@]}"; do
        if ! dpkg -l "$package" &> /dev/null; then
            print_status "Installing $package..."
            sudo apt install -y "$package"
        else
            print_status "$package already installed"
        fi
    done
}

# Install packages using yum (CentOS/RHEL)
install_with_yum() {
    local packages=("$@")
    for package in "${packages[@]}"; do
        if ! rpm -q "$package" &> /dev/null; then
            print_status "Installing $package..."
            sudo yum install -y "$package"
        else
            print_status "$package already installed"
        fi
    done
}

# Install packages using dnf (Fedora)
install_with_dnf() {
    local packages=("$@")
    for package in "${packages[@]}"; do
        if ! rpm -q "$package" &> /dev/null; then
            print_status "Installing $package..."
            sudo dnf install -y "$package"
        else
            print_status "$package already installed"
        fi
    done
}

# Install Node.js and NVM
install_nodejs() {
    local os=$(detect_os)

    if ! command -v node &> /dev/null; then
        print_status "Installing Node.js and NVM..."

        if [[ "$os" == "macos" ]]; then
            # Install Node.js via Homebrew
            install_with_homebrew "node"
        else
            # Install NVM on Linux
            curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
            source ~/.bashrc
            nvm install node
        fi

        # Install Yarn globally
        npm install -g yarn
    else
        print_status "Node.js already installed"
    fi
}

# Install Ruby and rbenv
install_ruby() {
    local os=$(detect_os)

    if ! command -v ruby &> /dev/null; then
        print_status "Installing Ruby and rbenv..."

        if [[ "$os" == "macos" ]]; then
            install_with_homebrew "rbenv" "ruby-build"
            rbenv install 3.2.2
            rbenv global 3.2.2
        else
            # Install Ruby via system package manager
            local pkg_manager=$(detect_package_manager)
            case "$pkg_manager" in
                "apt")
                    install_with_apt "ruby" "ruby-dev" "build-essential"
                    ;;
                "yum"|"dnf")
                    install_with_yum "ruby" "ruby-devel" "gcc"
                    ;;
            esac
        fi

        # Install Bundler
        gem install bundler
    else
        print_status "Ruby already installed"
    fi
}

# Install ASDF version manager
install_asdf() {
    local os=$(detect_os)

    if ! command -v asdf &> /dev/null; then
        print_status "Installing ASDF version manager..."

        if [[ "$os" == "macos" ]]; then
            install_with_homebrew "asdf"

            # Add ASDF to shell profile
            local homebrew_prefix="/opt/homebrew"
            if [[ ! -d "$homebrew_prefix" ]]; then
                homebrew_prefix="/usr/local"
            fi

            # Add to bash profile if not already present
            if ! grep -q "asdf.sh" ~/.bash_profile 2>/dev/null; then
                echo ". $homebrew_prefix/opt/asdf/libexec/asdf.sh" >> ~/.bash_profile
                echo ". $homebrew_prefix/opt/asdf/etc/bash_completion.d/asdf.bash" >> ~/.bash_profile
            fi

            # Source ASDF for current session
            . "$homebrew_prefix/opt/asdf/libexec/asdf.sh"
        else
            # Install ASDF on Linux
            git clone https://github.com/asdf-vm/asdf.git ~/.asdf --branch v0.13.1

            # Add to bash profile if not already present
            if ! grep -q "asdf.sh" ~/.bashrc 2>/dev/null; then
                echo '. "$HOME/.asdf/asdf.sh"' >> ~/.bashrc
                echo '. "$HOME/.asdf/completions/asdf.bash"' >> ~/.bashrc
            fi

            # Source ASDF for current session
            . "$HOME/.asdf/asdf.sh"
        fi
    else
        print_status "ASDF already installed"
    fi
}

# Install Golang via ASDF
install_golang() {
    local os=$(detect_os)

    if ! command -v go &> /dev/null; then
        print_status "Installing Golang via ASDF..."

        # Ensure ASDF is available
        if ! command -v asdf &> /dev/null; then
            install_asdf
        fi

        # Add golang plugin if not already added
        if ! asdf plugin list | grep -q golang; then
            asdf plugin add golang https://github.com/kennyp/asdf-golang.git
        fi

        # Install latest stable version of Go
        local go_version=$(asdf list all golang | grep -E '^[0-9]+\.[0-9]+\.[0-9]+$' | tail -1)
        if [[ -n "$go_version" ]]; then
            asdf install golang "$go_version"
            asdf global golang "$go_version"
            print_status "Installed Golang $go_version via ASDF"
        else
            print_warning "Could not determine latest Golang version, installing 1.21.5"
            asdf install golang 1.21.5
            asdf global golang 1.21.5
        fi
    else
        print_status "Golang already installed"
    fi
}

# Install Python tools
install_python() {
    local os=$(detect_os)

    if ! command -v python3 &> /dev/null; then
        print_status "Installing Python..."

        if [[ "$os" == "macos" ]]; then
            install_with_homebrew "python@3.8"
        else
            local pkg_manager=$(detect_package_manager)
            case "$pkg_manager" in
                "apt")
                    install_with_apt "python3" "python3-pip"
                    ;;
                "yum"|"dnf")
                    install_with_yum "python3" "python3-pip"
                    ;;
            esac
        fi
    else
        print_status "Python already installed"
    fi

    # Install pipx
    python3 -m pip install --user pipx
}

# Install Docker Desktop (macOS only - manual installation required)
install_docker() {
    local os=$(detect_os)

    if ! command -v docker &> /dev/null; then
        if [[ "$os" == "macos" ]]; then
            print_warning "Docker Desktop for macOS must be installed manually."
            print_warning "Please download and install Docker Desktop from:"
            print_warning "https://www.docker.com/products/docker-desktop/"
            print_warning "After installation, start Docker Desktop from Applications."
        else
            print_status "Installing Docker..."
            local pkg_manager=$(detect_package_manager)
            case "$pkg_manager" in
                "apt")
                    # Install Docker on Ubuntu/Debian
                    sudo apt update
                    sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
                    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
                    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
                    sudo apt update
                    sudo apt install -y docker-ce docker-ce-cli containerd.io
                    sudo usermod -aG docker $USER
                    ;;
                "yum"|"dnf")
                    # Install Docker on CentOS/RHEL/Fedora
                    sudo yum install -y yum-utils
                    sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
                    sudo yum install -y docker-ce docker-ce-cli containerd.io
                    sudo systemctl start docker
                    sudo systemctl enable docker
                    sudo usermod -aG docker $USER
                    ;;
            esac
        fi
    else
        print_status "Docker already installed"
    fi
}

# Main installation function
main() {
    print_status "Detecting platform..."
    detect_platform

    local os=$(detect_os)
    local pkg_manager=$(detect_package_manager)

    if [[ "$os" == "unknown" ]]; then
        print_error "Unsupported operating system"
        exit 1
    fi

    print_status "Installing dependencies for $os..."

    # Install package manager if needed
    if [[ "$os" == "macos" ]]; then
        install_homebrew
    fi

    # Install core tools
    print_status "Installing core tools..."
    if [[ "$os" == "macos" ]]; then
        install_with_homebrew "git" "jq" "curl"
    else
        case "$pkg_manager" in
            "apt")
                install_with_apt "git" "jq" "curl"
                ;;
            "yum"|"dnf")
                install_with_yum "git" "jq" "curl"
                ;;
            *)
                print_warning "No supported package manager found. Please install git, jq, and curl manually."
                ;;
        esac
    fi

    # Install ASDF version manager first (for macOS)
    if [[ "$os" == "macos" ]]; then
        install_asdf
    fi

    # Install language runtimes
    print_status "Installing language runtimes..."

    # Install Golang via ASDF
    install_golang

    # Install Java
    if [[ "$os" == "macos" ]]; then
        install_with_homebrew "openjdk"
    else
        case "$pkg_manager" in
            "apt")
                install_with_apt "openjdk-11-jdk"
                ;;
            "yum"|"dnf")
                install_with_yum "java-11-openjdk-devel"
                ;;
        esac
    fi

    # Install Node.js
    install_nodejs

    # Install Ruby
    install_ruby

    # Install Python
    install_python

    # Install cloud tools
    print_status "Installing cloud tools..."
    if [[ "$os" == "macos" ]]; then
        install_with_homebrew "awscli" "kubectl" "terraform"
        brew install heroku/brew/heroku
    else
        case "$pkg_manager" in
            "apt")
                install_with_apt "awscli" "kubectl"
                # Install Terraform
                curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
                sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
                sudo apt update
                sudo apt install terraform
                ;;
            "yum"|"dnf")
                install_with_yum "awscli" "kubectl"
                # Install Terraform
                sudo yum-config-manager --add-repo https://rpm.releases.hashicorp.com/RHEL/hashicorp.repo
                sudo yum install terraform
                ;;
        esac
    fi

    # Install Docker (separate function for platform-specific handling)
    install_docker

    # Install development tools
    print_status "Installing development tools..."
    if [[ "$os" == "macos" ]]; then
        install_with_homebrew "ansible" "starship"
    else
        case "$pkg_manager" in
            "apt")
                install_with_apt "ansible"
                # Install Starship
                curl -sS https://starship.rs/install.sh | sh
                ;;
            "yum"|"dnf")
                install_with_yum "ansible"
                # Install Starship
                curl -sS https://starship.rs/install.sh | sh
                ;;
        esac
    fi

    # Install security tools
    print_status "Installing security tools..."
    if [[ "$os" == "macos" ]]; then
        install_with_homebrew "gnupg"
    else
        case "$pkg_manager" in
            "apt")
                install_with_apt "gnupg"
                ;;
            "yum"|"dnf")
                install_with_yum "gnupg"
                ;;
        esac
    fi

    print_status "Dependencies installed successfully!"
    print_status "Run './check-dependencies.sh' to verify installation"
}

# Run main function
main "$@"
