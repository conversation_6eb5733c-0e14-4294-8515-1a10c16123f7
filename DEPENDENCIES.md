# Dependencies and Requirements

This document lists all the tools, languages, and dependencies required for the basher system to function properly.

## 🖥️ System Requirements

### Operating System
- **macOS** (10.15+)
- **Linux** (Ubuntu 18.04+, CentOS 7+, Fedora 30+)
- **Windows** (not supported - use WSL or Git Bash)

### Shell
- **Bash** 4.0+ (required)
- **Git** (required for version control)

## 📦 Package Managers

### Required
- **[Homebrew](https://brew.sh/)** - Package manager for macOS
  ```bash
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
  ```
- **System Package Manager** - For Linux distributions
  - **apt** (Ubuntu/Debian)
  - **yum** (CentOS/RHEL)
  - **dnf** (Fedora)

### Optional
- **[ASDF](https://asdf-vm.com/)** - Version manager for multiple languages
  ```bash
  brew install asdf
  ```

## 🔧 Core Tools

### Required
- **Git** - Version control system
  ```bash
  brew install git
  ```
- **SSH** - Secure shell (usually pre-installed)
- **curl** - HTTP client (usually pre-installed)
- **jq** - JSON processor
  ```bash
  brew install jq
  ```

### Optional
- **sed** - Stream editor (usually pre-installed)
- **xargs** - Command line utility (usually pre-installed)

## 🌐 Language Runtimes

### Go
- **Go** - Programming language
  ```bash
  brew install go
  ```
- **GOPATH** - Go workspace (automatically configured)

### Node.js
- **NVM** - Node Version Manager
  ```bash
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
  ```
- **Node.js** - JavaScript runtime
  ```bash
  nvm install node
  ```
- **Yarn** - Package manager
  ```bash
  npm install -g yarn
  ```

### Ruby
- **rbenv** - Ruby version manager
  ```bash
  brew install rbenv ruby-build
  ```
- **Ruby** - Programming language
  ```bash
  rbenv install 3.2.2
  rbenv global 3.2.2
  ```
- **Bundler** - Ruby gem manager
  ```bash
  gem install bundler
  ```

### Python
- **Python 3.8+** - Programming language
  ```bash
  brew install python@3.8
  ```
- **pipx** - Python package installer
  ```bash
  python3 -m pip install --user pipx
  ```

### Java
- **OpenJDK** - Java Development Kit
  ```bash
  brew install openjdk
  ```

## ☁️ Cloud & Infrastructure Tools

### AWS
- **AWS CLI** - Amazon Web Services command line
  ```bash
  brew install awscli
  ```
- **Docker** - Container platform (for eksctl)
  ```bash
  brew install --cask docker
  ```

### Kubernetes
- **kubectl** - Kubernetes command line tool
  ```bash
  brew install kubectl
  ```

### Terraform
- **Terraform** - Infrastructure as code
  ```bash
  brew install terraform
  ```

### Heroku
- **Heroku CLI** - Heroku command line
  ```bash
  brew install heroku/brew/heroku
  ```

## 🛠️ Development Tools

### Ansible
- **Ansible** - Configuration management
  ```bash
  brew install ansible
  ```

### Starship
- **Starship** - Cross-shell prompt
  ```bash
  brew install starship
  ```

### iTerm2 (macOS)
- **iTerm2** - Terminal emulator
  ```bash
  brew install --cask iterm2
  ```

## 🔐 Security Tools

### GPG
- **GPG** - GNU Privacy Guard
  ```bash
  brew install gnupg
  ```

### SSH Keys
- **SSH keys** - Secure shell keys
  ```bash
  ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
  ```

## 📋 Installation Script

### Quick Setup (macOS)
```bash
#!/bin/bash
# Quick setup script for basher dependencies

echo "Installing basher dependencies..."

# Install Homebrew if not present
if ! command -v brew &> /dev/null; then
    echo "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# Core tools
echo "Installing core tools..."
brew install git jq curl

# Language runtimes
echo "Installing language runtimes..."
brew install go python@3.8 openjdk

# Node.js and NVM
echo "Installing Node.js..."
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install node
npm install -g yarn

# Ruby and rbenv
echo "Installing Ruby..."
brew install rbenv ruby-build
rbenv install 3.2.2
rbenv global 3.2.2
gem install bundler

# Python tools
echo "Installing Python tools..."
python3 -m pip install --user pipx

# Cloud tools
echo "Installing cloud tools..."
brew install awscli kubectl terraform
brew install heroku/brew/heroku
brew install --cask docker

# Development tools
echo "Installing development tools..."
brew install ansible starship

# Security tools
echo "Installing security tools..."
brew install gnupg

echo "Dependencies installed successfully!"
```

## 🔍 Dependency Checker

### Check Required Tools
```bash
#!/bin/bash
# Dependency checker script

echo "Checking basher dependencies..."

# Core tools
echo "Core tools:"
command -v git >/dev/null 2>&1 && echo "✅ Git" || echo "❌ Git"
command -v curl >/dev/null 2>&1 && echo "✅ curl" || echo "❌ curl"
command -v jq >/dev/null 2>&1 && echo "✅ jq" || echo "❌ jq"

# Language runtimes
echo "Language runtimes:"
command -v go >/dev/null 2>&1 && echo "✅ Go" || echo "❌ Go"
command -v node >/dev/null 2>&1 && echo "✅ Node.js" || echo "❌ Node.js"
command -v ruby >/dev/null 2>&1 && echo "✅ Ruby" || echo "❌ Ruby"
command -v python3 >/dev/null 2>&1 && echo "✅ Python" || echo "❌ Python"
command -v java >/dev/null 2>&1 && echo "✅ Java" || echo "❌ Java"

# Cloud tools
echo "Cloud tools:"
command -v aws >/dev/null 2>&1 && echo "✅ AWS CLI" || echo "❌ AWS CLI"
command -v kubectl >/dev/null 2>&1 && echo "✅ kubectl" || echo "❌ kubectl"
command -v terraform >/dev/null 2>&1 && echo "✅ Terraform" || echo "❌ Terraform"
command -v heroku >/dev/null 2>&1 && echo "✅ Heroku CLI" || echo "❌ Heroku CLI"
command -v docker >/dev/null 2>&1 && echo "✅ Docker" || echo "❌ Docker"

# Development tools
echo "Development tools:"
command -v ansible >/dev/null 2>&1 && echo "✅ Ansible" || echo "❌ Ansible"
command -v starship >/dev/null 2>&1 && echo "✅ Starship" || echo "❌ Starship"

echo "Dependency check complete!"
```

## 📝 Notes

### Optional Dependencies
Some modules will work without all dependencies installed. The system will:
- Skip modules that depend on missing tools
- Show warnings for missing optional dependencies
- Continue to function with available tools

### Platform Differences
- **macOS**: All tools available via Homebrew
- **Linux**: Use system package manager (apt, yum, etc.)
- **Windows**: Use Chocolatey or manual installation

### Version Requirements
- **Go**: 1.16+
- **Node.js**: 14+
- **Ruby**: 2.7+
- **Python**: 3.8+
- **Java**: 8+
