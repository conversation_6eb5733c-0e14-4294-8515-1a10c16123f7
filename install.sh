#!/bin/bash
# Basher - Modular Bash Profile Installer
# This script sources all modules to create a consistent bash environment

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to source a module if it exists
source_module() {
    local module_path="$SCRIPT_DIR/modules/$1"
    if [[ -f "$module_path" ]]; then
        source "$module_path"
    else
        echo "Warning: Module $1 not found at $module_path"
    fi
}

# Function to source all modules in a directory
source_module_dir() {
    local dir_path="$SCRIPT_DIR/modules/$1"
    if [[ -d "$dir_path" ]]; then
        for module in "$dir_path"/*.sh; do
            if [[ -f "$module" ]]; then
                source "$module"
            fi
        done
    fi
}

# Check if running interactively
case $- in
  *i*) ;;
    *) return;;
esac

# Source core modules first
echo "Loading core modules..."
source_module_dir "core"

# Source language modules
echo "Loading language modules..."
source_module_dir "languages"

# Source cloud modules
echo "Loading cloud modules..."
source_module_dir "cloud"

# Source tools modules
echo "Loading tools modules..."
source_module_dir "tools"

# Source security modules
echo "Loading security modules..."
source_module_dir "security"

# Source productivity modules
echo "Loading productivity modules..."
source_module_dir "productivity"

# Source company modules
echo "Loading company modules..."
source_module_dir "company"

# Set up prompt with git integration
export PS1="\[\e[36m\][\[\e[m\]\[\e[36m\]\u\[\e[m\]\[\e[36m\]@\[\e[m\]\[\e[36m\]\h\[\e[m\] \[\e[36m\]\w\[\e[m\]\[\e[33m\]\`parse_git_branch\`\[\e[m\]\[\e[36m\]]\[\e[m\]:\[\e[33m\]\`nonzero_return\`\[\e[m\]\[\e[31;40m\]\\$\[\e[m\] "

# Source platform detection
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/scripts/platform-detect.sh"

# Set up package manager based on platform
local os=$(detect_os)
if [[ "$os" == "macos" ]]; then
    # Set up Homebrew
    local homebrew_prefix=$(get_homebrew_prefix)
    if [[ -n "$homebrew_prefix" ]]; then
        eval "$($homebrew_prefix/bin/brew shellenv)"

        # Set up ASDF if available
        if [[ -f "$homebrew_prefix/opt/asdf/libexec/asdf.sh" ]]; then
            . "$homebrew_prefix/opt/asdf/libexec/asdf.sh"
            . "$homebrew_prefix/opt/asdf/etc/bash_completion.d/asdf.bash"
        fi
    fi
fi

# Set up bash completion
if type brew &>/dev/null; then
  HOMEBREW_PREFIX="$(brew --prefix)"
  if [[ -r "${HOMEBREW_PREFIX}/etc/profile.d/bash_completion.sh" ]]; then
    source "${HOMEBREW_PREFIX}/etc/profile.d/bash_completion.sh"
  else
    for COMPLETION in "${HOMEBREW_PREFIX}/etc/bash_completion.d/"*; do
      [[ -r "${COMPLETION}" ]] && source "${COMPLETION}"
    done
  fi
fi

# iTerm2 integration
test -e "${HOME}/.iterm2_shell_integration.bash" && source "${HOME}/.iterm2_shell_integration.bash"

echo "Basher modules loaded successfully!"
